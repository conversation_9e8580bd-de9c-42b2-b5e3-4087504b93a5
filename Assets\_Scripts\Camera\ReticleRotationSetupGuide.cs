using UnityEngine;
using Unity.Cinemachine;
using BTR.CombatUI;

namespace BTR
{
    /// <summary>
    /// Helper component to automatically set up reticle-based camera rotation on Cinemachine cameras.
    /// This is a convenience script that can be used to quickly add reticle rotation to existing cameras.
    /// </summary>
    [AddComponentMenu("BTR/Camera/Reticle Rotation Setup Guide")]
    public class ReticleRotationSetupGuide : MonoBehaviour
    {
        [Header("Setup Configuration")]
        [Tooltip("The CinemachineCamera to add reticle rotation to")]
        public CinemachineCamera targetCamera;

        [Tooltip("Automatically find CinemachineCamera on this GameObject if not specified")]
        public bool autoFindCamera = true;

        [Header("Default Settings")]
        [Tooltip("Preset configuration to apply")]
        public ReticleRotationPreset preset = ReticleRotationPreset.Moderate;

        [Header("Custom Settings (if preset is Custom)")]
        [Range(0f, 30f)]
        public float customMaxRotationX = 15f;
        [Range(0f, 30f)]
        public float customMaxRotationY = 10f;
        [Range(0.1f, 3f)]
        public float customHorizontalSensitivity = 1f;
        [Range(0.1f, 3f)]
        public float customVerticalSensitivity = 1f;
        [Range(0f, 2f)]
        public float customDampingTime = 0.1f;

        public enum ReticleRotationPreset
        {
            Subtle,
            Moderate,
            Pronounced,
            Custom
        }

        [System.Serializable]
        public struct PresetSettings
        {
            public float maxRotationX;
            public float maxRotationY;
            public float horizontalSensitivity;
            public float verticalSensitivity;
            public float dampingTime;
        }

        private static readonly PresetSettings[] presetConfigurations = new PresetSettings[]
        {
            // Subtle
            new PresetSettings { maxRotationX = 8f, maxRotationY = 5f, horizontalSensitivity = 0.8f, verticalSensitivity = 0.6f, dampingTime = 0.2f },
            // Moderate
            new PresetSettings { maxRotationX = 15f, maxRotationY = 10f, horizontalSensitivity = 1f, verticalSensitivity = 1f, dampingTime = 0.1f },
            // Pronounced
            new PresetSettings { maxRotationX = 25f, maxRotationY = 18f, horizontalSensitivity = 1.5f, verticalSensitivity = 1.3f, dampingTime = 0.05f },
            // Custom (not used)
            new PresetSettings()
        };

        private void Start()
        {
            // Auto-setup if enabled
            if (autoFindCamera && targetCamera == null)
            {
                targetCamera = GetComponent<CinemachineCamera>();
            }
        }

        /// <summary>
        /// Add reticle rotation extension to the target camera with the specified preset
        /// </summary>
        [ContextMenu("Setup Reticle Rotation")]
        public void SetupReticleRotation()
        {
            if (targetCamera == null)
            {
                if (autoFindCamera)
                {
                    targetCamera = GetComponent<CinemachineCamera>();
                }

                if (targetCamera == null)
                {
                    Debug.LogError("[ReticleRotationSetupGuide] No CinemachineCamera found. Please assign one in the inspector.");
                    return;
                }
            }

            // Check if extension already exists
            CinemachineReticleRotation existingExtension = targetCamera.GetComponent<CinemachineReticleRotation>();
            if (existingExtension != null)
            {
                Debug.LogWarning("[ReticleRotationSetupGuide] CinemachineReticleRotation already exists on this camera. Updating settings instead.");
                ApplySettings(existingExtension);
                return;
            }

            // Add the extension
            CinemachineReticleRotation reticleRotation = targetCamera.gameObject.AddComponent<CinemachineReticleRotation>();
            ApplySettings(reticleRotation);

            Debug.Log($"[ReticleRotationSetupGuide] Successfully added CinemachineReticleRotation to {targetCamera.name} with {preset} preset.");
        }

        /// <summary>
        /// Remove reticle rotation extension from the target camera
        /// </summary>
        [ContextMenu("Remove Reticle Rotation")]
        public void RemoveReticleRotation()
        {
            if (targetCamera == null)
            {
                Debug.LogError("[ReticleRotationSetupGuide] No target camera specified.");
                return;
            }

            CinemachineReticleRotation extension = targetCamera.GetComponent<CinemachineReticleRotation>();
            if (extension != null)
            {
                if (Application.isPlaying)
                {
                    Destroy(extension);
                }
                else
                {
                    DestroyImmediate(extension);
                }
                Debug.Log($"[ReticleRotationSetupGuide] Removed CinemachineReticleRotation from {targetCamera.name}.");
            }
            else
            {
                Debug.LogWarning("[ReticleRotationSetupGuide] No CinemachineReticleRotation found on the target camera.");
            }
        }

        /// <summary>
        /// Check if the setup is valid and ready to use
        /// </summary>
        public bool ValidateSetup()
        {
            // Check for CinemachineCamera
            if (targetCamera == null)
            {
                Debug.LogError("[ReticleRotationSetupGuide] No CinemachineCamera assigned.");
                return false;
            }

            // Check for CinemachineReticleRotation extension
            CinemachineReticleRotation extension = targetCamera.GetComponent<CinemachineReticleRotation>();
            if (extension == null)
            {
                Debug.LogWarning("[ReticleRotationSetupGuide] CinemachineReticleRotation extension not found on camera.");
                return false;
            }

            // Check for UIReticleController in scene
            if (UIReticleController.Instance == null)
            {
                Debug.LogWarning("[ReticleRotationSetupGuide] UIReticleController.Instance not found in scene. Make sure UIReticleController is active.");
                return false;
            }

            Debug.Log("[ReticleRotationSetupGuide] Setup validation passed. Reticle rotation should be working.");
            return true;
        }

        private void ApplySettings(CinemachineReticleRotation reticleRotation)
        {
            if (preset == ReticleRotationPreset.Custom)
            {
                reticleRotation.maxRotationX = customMaxRotationX;
                reticleRotation.maxRotationY = customMaxRotationY;
                reticleRotation.horizontalSensitivity = customHorizontalSensitivity;
                reticleRotation.verticalSensitivity = customVerticalSensitivity;
                reticleRotation.dampingTime = customDampingTime;
            }
            else
            {
                PresetSettings settings = presetConfigurations[(int)preset];
                reticleRotation.maxRotationX = settings.maxRotationX;
                reticleRotation.maxRotationY = settings.maxRotationY;
                reticleRotation.horizontalSensitivity = settings.horizontalSensitivity;
                reticleRotation.verticalSensitivity = settings.verticalSensitivity;
                reticleRotation.dampingTime = settings.dampingTime;
            }

            reticleRotation.enableReticleRotation = true;
        }

        #if UNITY_EDITOR
        private void OnValidate()
        {
            if (autoFindCamera && targetCamera == null)
            {
                targetCamera = GetComponent<CinemachineCamera>();
            }
        }
        #endif
    }
}
