using Unity.Cinemachine;
using UnityEngine;
using BTR.CombatUI;

namespace BTR
{
    /// <summary>
    /// Cinemachine extension that applies subtle camera rotation based on reticle position.
    /// The camera will tilt slightly in the direction of the reticle movement while maintaining
    /// its follow target functionality. This creates a more dynamic and responsive camera feel.
    /// </summary>
    [AddComponentMenu("Cinemachine/Reticle Rotation Extension")]
    public class CinemachineReticleRotation : CinemachineExtension
    {
        [Head<PERSON>("Reticle Rotation Settings")]
        [Tooltip("Enable/disable reticle-based camera rotation")]
        public bool enableReticleRotation = true;

        [Header("Rotation Sensitivity")]
        [Tooltip("Maximum rotation angle on X-axis (horizontal) in degrees")]
        [Range(0f, f)]
        public float maxRotationX = 15f;

        [Tooltip("Maximum rotation angle on Y-axis (vertical) in degrees")]
        [Range(0f, 30f)]
        public float maxRotationY = 10f;

        [Tooltip("Sensitivity multiplier for horizontal rotation")]
        [Range(0.1f, 3f)]
        public float horizontalSensitivity = 1f;

        [Tooltip("Sensitivity multiplier for vertical rotation")]
        [Range(0.1f, 3f)]
        public float verticalSensitivity = 1f;

        [Header("Smoothing")]
        [Tooltip("Damping time for rotation changes (0 = instant, higher = smoother)")]
        [Range(0f, 2f)]
        public float dampingTime = 0.1f;

        [Header("Dead Zone")]
        [Tooltip("Dead zone around screen center where no rotation is applied (0-0.5)")]
        [Range(0f, 0.5f)]
        public float deadZone = 0.05f;

        [Header("Debug")]
        [Tooltip("Show debug information in the inspector")]
        public bool showDebugInfo = false;

        // Internal state
        private Vector2 currentRotationOffset = Vector2.zero;
        private Vector2 targetRotationOffset = Vector2.zero;
        private Vector2 rotationVelocity = Vector2.zero;

        // Debug info
        [System.NonSerialized]
        public Vector2 debugReticlePosition;
        [System.NonSerialized]
        public Vector2 debugRotationOffset;

        protected override void PostPipelineStageCallback(
            CinemachineVirtualCameraBase vcam,
            CinemachineCore.Stage stage,
            ref CameraState state,
            float deltaTime)
        {
            // Apply rotation at the Finalize stage to ensure it happens after all other calculations
            if (stage == CinemachineCore.Stage.Finalize && enableReticleRotation)
            {
                ApplyReticleRotation(ref state, deltaTime);
            }
        }

        private void ApplyReticleRotation(ref CameraState state, float deltaTime)
        {
            // Check if UIReticleController is available
            if (UIReticleController.Instance == null)
            {
                return;
            }

            // Get normalized reticle position (0-1 range)
            Vector2 reticlePos = UIReticleController.Instance.NormalizedScreenPosition;
            
            // Store for debug display
            debugReticlePosition = reticlePos;

            // Convert to centered coordinates (-0.5 to 0.5)
            Vector2 centeredPos = reticlePos - Vector2.one * 0.5f;

            // Apply dead zone
            if (Mathf.Abs(centeredPos.x) < deadZone) centeredPos.x = 0f;
            if (Mathf.Abs(centeredPos.y) < deadZone) centeredPos.y = 0f;

            // Calculate target rotation offsets
            targetRotationOffset.x = centeredPos.x * maxRotationX * horizontalSensitivity;
            targetRotationOffset.y = centeredPos.y * maxRotationY * verticalSensitivity;

            // Apply smoothing if damping is enabled
            if (dampingTime > 0f)
            {
                currentRotationOffset.x = Mathf.SmoothDamp(
                    currentRotationOffset.x, 
                    targetRotationOffset.x, 
                    ref rotationVelocity.x, 
                    dampingTime, 
                    Mathf.Infinity, 
                    deltaTime);

                currentRotationOffset.y = Mathf.SmoothDamp(
                    currentRotationOffset.y, 
                    targetRotationOffset.y, 
                    ref rotationVelocity.y, 
                    dampingTime, 
                    Mathf.Infinity, 
                    deltaTime);
            }
            else
            {
                currentRotationOffset = targetRotationOffset;
            }

            // Store for debug display
            debugRotationOffset = currentRotationOffset;

            // Apply rotation to camera state
            // Note: Y rotation affects yaw (horizontal), X rotation affects pitch (vertical)
            Vector3 eulerOffset = new Vector3(-currentRotationOffset.y, currentRotationOffset.x, 0f);
            Quaternion rotationOffset = Quaternion.Euler(eulerOffset);

            // Apply the rotation offset to the final orientation
            state.RawOrientation = state.RawOrientation * rotationOffset;
        }

        /// <summary>
        /// Reset rotation offsets to zero (useful for camera transitions)
        /// </summary>
        public void ResetRotation()
        {
            currentRotationOffset = Vector2.zero;
            targetRotationOffset = Vector2.zero;
            rotationVelocity = Vector2.zero;
        }

        /// <summary>
        /// Set the sensitivity values programmatically
        /// </summary>
        public void SetSensitivity(float horizontal, float vertical)
        {
            horizontalSensitivity = Mathf.Clamp(horizontal, 0.1f, 3f);
            verticalSensitivity = Mathf.Clamp(vertical, 0.1f, 3f);
        }

        /// <summary>
        /// Set the maximum rotation angles programmatically
        /// </summary>
        public void SetMaxRotation(float maxX, float maxY)
        {
            maxRotationX = Mathf.Clamp(maxX, 0f, 30f);
            maxRotationY = Mathf.Clamp(maxY, 0f, 30f);
        }

        // Editor-only debug visualization
        #if UNITY_EDITOR
        private void OnValidate()
        {
            // Ensure values stay within reasonable ranges
            maxRotationX = Mathf.Clamp(maxRotationX, 0f, 30f);
            maxRotationY = Mathf.Clamp(maxRotationY, 0f, 30f);
            horizontalSensitivity = Mathf.Clamp(horizontalSensitivity, 0.1f, 3f);
            verticalSensitivity = Mathf.Clamp(verticalSensitivity, 0.1f, 3f);
            dampingTime = Mathf.Clamp(dampingTime, 0f, 2f);
            deadZone = Mathf.Clamp(deadZone, 0f, 0.5f);
        }
        #endif
    }
}
