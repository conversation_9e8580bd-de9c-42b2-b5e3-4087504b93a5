#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using Unity.Cinemachine;
using BTR;

namespace BTR.Editor
{
    [CustomEditor(typeof(ReticleRotationSetupGuide))]
    public class ReticleRotationSetupGuideEditor : UnityEditor.Editor
    {
        private SerializedProperty targetCamera;
        private SerializedProperty autoFindCamera;
        private SerializedProperty preset;
        private SerializedProperty customMaxRotationX;
        private SerializedProperty customMaxRotationY;
        private SerializedProperty customHorizontalSensitivity;
        private SerializedProperty customVerticalSensitivity;
        private SerializedProperty customDampingTime;

        private void OnEnable()
        {
            targetCamera = serializedObject.FindProperty("targetCamera");
            autoFindCamera = serializedObject.FindProperty("autoFindCamera");
            preset = serializedObject.FindProperty("preset");
            customMaxRotationX = serializedObject.FindProperty("customMaxRotationX");
            customMaxRotationY = serializedObject.FindProperty("customMaxRotationY");
            customHorizontalSensitivity = serializedObject.FindProperty("customHorizontalSensitivity");
            customVerticalSensitivity = serializedObject.FindProperty("customVerticalSensitivity");
            customDampingTime = serializedObject.FindProperty("customDampingTime");
        }

        public override void OnInspectorGUI()
        {
            ReticleRotationSetupGuide setupGuide = (ReticleRotationSetupGuide)target;
            serializedObject.Update();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Reticle Rotation Setup Guide", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox(
                "This helper component makes it easy to add reticle-based camera rotation to your Cinemachine cameras. " +
                "It will automatically add and configure the CinemachineReticleRotation extension.",
                MessageType.Info);

            EditorGUILayout.Space();

            // Camera setup
            EditorGUILayout.LabelField("Camera Configuration", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(autoFindCamera, new GUIContent("Auto Find Camera", "Automatically find CinemachineCamera on this GameObject"));
            
            if (!autoFindCamera.boolValue || targetCamera.objectReferenceValue != null)
            {
                EditorGUILayout.PropertyField(targetCamera, new GUIContent("Target Camera", "The CinemachineCamera to add reticle rotation to"));
            }

            // Show current camera status
            CinemachineCamera currentCamera = autoFindCamera.boolValue && targetCamera.objectReferenceValue == null 
                ? setupGuide.GetComponent<CinemachineCamera>() 
                : targetCamera.objectReferenceValue as CinemachineCamera;

            if (currentCamera != null)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField($"Target: {currentCamera.name}", EditorStyles.miniLabel);
                
                // Check if extension already exists
                CinemachineReticleRotation existingExtension = currentCamera.GetComponent<CinemachineReticleRotation>();
                if (existingExtension != null)
                {
                    EditorGUILayout.HelpBox("✓ CinemachineReticleRotation extension already exists on this camera.", MessageType.Info);
                }
                else
                {
                    EditorGUILayout.HelpBox("CinemachineReticleRotation extension not found. Use 'Setup Reticle Rotation' to add it.", MessageType.Warning);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No CinemachineCamera found. Please assign one or enable Auto Find Camera.", MessageType.Error);
            }

            EditorGUILayout.Space();

            // Preset configuration
            EditorGUILayout.LabelField("Rotation Configuration", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(preset, new GUIContent("Preset", "Preset configuration to apply"));

            // Show custom settings if Custom preset is selected
            if (preset.enumValueIndex == (int)ReticleRotationSetupGuide.ReticleRotationPreset.Custom)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Custom Settings", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(customMaxRotationX, new GUIContent("Max Horizontal Rotation"));
                EditorGUILayout.PropertyField(customMaxRotationY, new GUIContent("Max Vertical Rotation"));
                EditorGUILayout.PropertyField(customHorizontalSensitivity, new GUIContent("Horizontal Sensitivity"));
                EditorGUILayout.PropertyField(customVerticalSensitivity, new GUIContent("Vertical Sensitivity"));
                EditorGUILayout.PropertyField(customDampingTime, new GUIContent("Damping Time"));
            }
            else
            {
                // Show preset description
                string presetDescription = GetPresetDescription((ReticleRotationSetupGuide.ReticleRotationPreset)preset.enumValueIndex);
                EditorGUILayout.HelpBox(presetDescription, MessageType.Info);
            }

            EditorGUILayout.Space();

            // Action buttons
            EditorGUILayout.LabelField("Actions", EditorStyles.boldLabel);
            
            EditorGUILayout.BeginHorizontal();
            
            GUI.enabled = currentCamera != null;
            if (GUILayout.Button("Setup Reticle Rotation", GUILayout.Height(30)))
            {
                setupGuide.SetupReticleRotation();
            }
            
            if (GUILayout.Button("Remove Reticle Rotation", GUILayout.Height(30)))
            {
                setupGuide.RemoveReticleRotation();
            }
            
            GUI.enabled = true;
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space();

            if (GUILayout.Button("Validate Setup"))
            {
                setupGuide.ValidateSetup();
            }

            EditorGUILayout.Space();

            // Quick info section
            EditorGUILayout.LabelField("Quick Info", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox(
                "• Subtle: Gentle camera movement, good for cinematic feel\n" +
                "• Moderate: Balanced responsiveness, good for most games\n" +
                "• Pronounced: Strong camera movement, good for action games\n" +
                "• Custom: Define your own settings",
                MessageType.None);

            serializedObject.ApplyModifiedProperties();
        }

        private string GetPresetDescription(ReticleRotationSetupGuide.ReticleRotationPreset preset)
        {
            switch (preset)
            {
                case ReticleRotationSetupGuide.ReticleRotationPreset.Subtle:
                    return "Subtle: Gentle camera movement with reduced sensitivity. Good for cinematic or exploration games.";
                case ReticleRotationSetupGuide.ReticleRotationPreset.Moderate:
                    return "Moderate: Balanced responsiveness with standard sensitivity. Good for most game types.";
                case ReticleRotationSetupGuide.ReticleRotationPreset.Pronounced:
                    return "Pronounced: Strong camera movement with increased sensitivity. Good for action or competitive games.";
                default:
                    return "";
            }
        }
    }
}
#endif
