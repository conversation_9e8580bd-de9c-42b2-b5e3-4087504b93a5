# Cinemachine Reticle Rotation System

This system provides reticle-based camera rotation for Cinemachine v3 cameras, allowing the camera to subtly rotate based on the reticle's position on screen while maintaining all of Cinemachine's follow and tracking functionality.

## Overview

The system consists of three main components:

1. **CinemachineReticleRotation** - The core extension that applies rotation based on reticle position
2. **ReticleRotationSetupGuide** - A helper component for easy setup and configuration
3. **Custom Editors** - Enhanced inspector interfaces for both components

## Features

- ✅ **Cinemachine v3 Compatible** - Uses proper CinemachineExtension architecture
- ✅ **Maintains Follow Functionality** - Only affects rotation, translation remains unchanged
- ✅ **Configurable Sensitivity** - Separate X and Y axis sensitivity controls
- ✅ **Multiple Presets** - Subtle, Moderate, Pronounced, and Custom configurations
- ✅ **Smooth Damping** - Optional smoothing for rotation changes
- ✅ **Dead Zone Support** - Configurable dead zone around screen center
- ✅ **Debug Visualization** - Real-time debug information in the inspector
- ✅ **Easy Integration** - Works with existing UIReticleController without modification

## Quick Setup

### Method 1: Using Setup Guide (Recommended)

1. Add `ReticleRotationSetupGuide` component to your CinemachineCamera GameObject
2. Choose a preset (Subtle, Moderate, or Pronounced)
3. Click "Setup Reticle Rotation" button
4. Done! The system is now active

### Method 2: Manual Setup

1. Add `CinemachineReticleRotation` component to your CinemachineCamera GameObject
2. Configure the settings in the inspector
3. Ensure `UIReticleController` is active in your scene

## Configuration Options

### Rotation Limits
- **Max Horizontal Rotation** (0-30°): Maximum rotation angle on X-axis
- **Max Vertical Rotation** (0-30°): Maximum rotation angle on Y-axis

### Sensitivity
- **Horizontal Sensitivity** (0.1-3.0): Multiplier for horizontal rotation response
- **Vertical Sensitivity** (0.1-3.0): Multiplier for vertical rotation response

### Fine Tuning
- **Damping Time** (0-2s): Smoothing time for rotation changes (0 = instant)
- **Dead Zone** (0-0.5): Area around screen center with no rotation applied

## Presets

### Subtle
- Max Rotation: 8° horizontal, 5° vertical
- Sensitivity: 0.8x horizontal, 0.6x vertical
- Damping: 0.2s
- **Best for**: Cinematic games, exploration, story-driven experiences

### Moderate (Default)
- Max Rotation: 15° horizontal, 10° vertical
- Sensitivity: 1.0x horizontal, 1.0x vertical
- Damping: 0.1s
- **Best for**: Most game types, balanced responsiveness

### Pronounced
- Max Rotation: 25° horizontal, 18° vertical
- Sensitivity: 1.5x horizontal, 1.3x vertical
- Damping: 0.05s
- **Best for**: Action games, competitive games, high-intensity gameplay

## Integration with Existing Systems

### UIReticleController Integration
The system automatically reads from `UIReticleController.Instance.NormalizedScreenPosition`, so no modifications to your existing reticle system are required.

### Cinemachine Pipeline
The extension operates at the `CinemachineCore.Stage.Finalize` stage, ensuring it applies after all other camera calculations while still allowing for proper blending between cameras.

### Existing Camera Rotation
If you have existing camera rotation logic (like the `objectToRotate` in UIReticleController), you can:
- Disable it by setting `objectToRotate` to null
- Keep both systems running (though this may cause conflicts)
- Use the Cinemachine approach for new cameras

## API Reference

### CinemachineReticleRotation

```csharp
// Enable/disable the effect
reticleRotation.enableReticleRotation = true;

// Set sensitivity programmatically
reticleRotation.SetSensitivity(1.2f, 0.8f);

// Set maximum rotation angles
reticleRotation.SetMaxRotation(20f, 15f);

// Reset rotation to zero (useful for camera transitions)
reticleRotation.ResetRotation();
```

### ReticleRotationSetupGuide

```csharp
// Setup with current preset
setupGuide.SetupReticleRotation();

// Remove the extension
setupGuide.RemoveReticleRotation();

// Validate the setup
bool isValid = setupGuide.ValidateSetup();
```

## Troubleshooting

### Camera Not Rotating
1. Check that `UIReticleController.Instance` exists in the scene
2. Verify that `enableReticleRotation` is true
3. Ensure the reticle is moving outside the dead zone
4. Check that sensitivity values are not zero

### Rotation Too Subtle/Strong
1. Adjust the sensitivity multipliers
2. Increase/decrease the maximum rotation angles
3. Try a different preset
4. Reduce damping time for more responsive movement

### Conflicts with Existing Systems
1. Disable the `objectToRotate` functionality in UIReticleController
2. Ensure only one rotation system is active per camera
3. Check for other extensions that might modify camera rotation

## Performance Notes

- The extension is very lightweight, only performing calculations when the reticle moves
- No additional GameObjects or components are created
- Integrates seamlessly with Cinemachine's existing update cycle
- Minimal impact on frame rate

## Best Practices

1. **Start with presets** - Use the provided presets as starting points
2. **Test with your game** - Different game types benefit from different settings
3. **Consider your audience** - Some players are sensitive to camera movement
4. **Use dead zones** - Prevent jitter when the reticle is near screen center
5. **Enable damping** - Smooth rotation changes feel more natural
6. **Validate setup** - Use the validation tools to ensure everything is working

## Compatibility

- **Unity Version**: 2022.2.15+ (Cinemachine v3 requirement)
- **Cinemachine Version**: 3.0.1+
- **Input System**: Compatible with both Unity Input System and Legacy Input
- **Platforms**: All platforms supported by Unity and Cinemachine

## Support

For issues or questions:
1. Check the debug information in the inspector
2. Use the validation tools
3. Verify UIReticleController is active and working
4. Check Unity Console for any error messages
