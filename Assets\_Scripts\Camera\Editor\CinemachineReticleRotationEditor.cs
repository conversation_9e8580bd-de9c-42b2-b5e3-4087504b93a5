#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using BTR;

namespace BTR.Editor
{
    [CustomEditor(typeof(CinemachineReticleRotation))]
    public class CinemachineReticleRotationEditor : UnityEditor.Editor
    {
        private SerializedProperty enableReticleRotation;
        private SerializedProperty maxRotationX;
        private SerializedProperty maxRotationY;
        private SerializedProperty horizontalSensitivity;
        private SerializedProperty verticalSensitivity;
        private SerializedProperty dampingTime;
        private SerializedProperty deadZone;
        private SerializedProperty showDebugInfo;

        private void OnEnable()
        {
            enableReticleRotation = serializedObject.FindProperty("enableReticleRotation");
            maxRotationX = serializedObject.FindProperty("maxRotationX");
            maxRotationY = serializedObject.FindProperty("maxRotationY");
            horizontalSensitivity = serializedObject.FindProperty("horizontalSensitivity");
            verticalSensitivity = serializedObject.FindProperty("verticalSensitivity");
            dampingTime = serializedObject.FindProperty("dampingTime");
            deadZone = serializedObject.FindProperty("deadZone");
            showDebugInfo = serializedObject.FindProperty("showDebugInfo");
        }

        public override void OnInspectorGUI()
        {
            CinemachineReticleRotation reticleRotation = (CinemachineReticleRotation)target;
            serializedObject.Update();

            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Cinemachine Reticle Rotation", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox(
                "This extension applies subtle camera rotation based on reticle position. " +
                "The camera will tilt slightly in the direction of reticle movement while maintaining follow functionality.",
                MessageType.Info);

            EditorGUILayout.Space();

            // Main enable toggle
            EditorGUILayout.PropertyField(enableReticleRotation, new GUIContent("Enable Reticle Rotation"));

            if (enableReticleRotation.boolValue)
            {
                EditorGUILayout.Space();

                // Rotation limits
                EditorGUILayout.LabelField("Rotation Limits", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(maxRotationX, new GUIContent("Max Horizontal Rotation", "Maximum rotation angle on X-axis (horizontal) in degrees"));
                EditorGUILayout.PropertyField(maxRotationY, new GUIContent("Max Vertical Rotation", "Maximum rotation angle on Y-axis (vertical) in degrees"));

                EditorGUILayout.Space();

                // Sensitivity
                EditorGUILayout.LabelField("Sensitivity", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(horizontalSensitivity, new GUIContent("Horizontal Sensitivity", "Sensitivity multiplier for horizontal rotation"));
                EditorGUILayout.PropertyField(verticalSensitivity, new GUIContent("Vertical Sensitivity", "Sensitivity multiplier for vertical rotation"));

                EditorGUILayout.Space();

                // Smoothing and dead zone
                EditorGUILayout.LabelField("Fine Tuning", EditorStyles.boldLabel);
                EditorGUILayout.PropertyField(dampingTime, new GUIContent("Damping Time", "Damping time for rotation changes (0 = instant, higher = smoother)"));
                EditorGUILayout.PropertyField(deadZone, new GUIContent("Dead Zone", "Dead zone around screen center where no rotation is applied"));

                EditorGUILayout.Space();

                // Quick setup buttons
                EditorGUILayout.LabelField("Quick Setup", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();
                
                if (GUILayout.Button("Subtle"))
                {
                    maxRotationX.floatValue = 8f;
                    maxRotationY.floatValue = 5f;
                    horizontalSensitivity.floatValue = 0.8f;
                    verticalSensitivity.floatValue = 0.6f;
                    dampingTime.floatValue = 0.2f;
                }
                
                if (GUILayout.Button("Moderate"))
                {
                    maxRotationX.floatValue = 15f;
                    maxRotationY.floatValue = 10f;
                    horizontalSensitivity.floatValue = 1f;
                    verticalSensitivity.floatValue = 1f;
                    dampingTime.floatValue = 0.1f;
                }
                
                if (GUILayout.Button("Pronounced"))
                {
                    maxRotationX.floatValue = 25f;
                    maxRotationY.floatValue = 18f;
                    horizontalSensitivity.floatValue = 1.5f;
                    verticalSensitivity.floatValue = 1.3f;
                    dampingTime.floatValue = 0.05f;
                }
                
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space();

                // Reset button
                if (GUILayout.Button("Reset Rotation"))
                {
                    if (Application.isPlaying)
                    {
                        reticleRotation.ResetRotation();
                    }
                }
            }

            EditorGUILayout.Space();

            // Debug section
            EditorGUILayout.PropertyField(showDebugInfo, new GUIContent("Show Debug Info"));
            
            if (showDebugInfo.boolValue && Application.isPlaying)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Debug Information", EditorStyles.boldLabel);
                
                GUI.enabled = false;
                EditorGUILayout.Vector2Field("Reticle Position", reticleRotation.debugReticlePosition);
                EditorGUILayout.Vector2Field("Rotation Offset", reticleRotation.debugRotationOffset);
                GUI.enabled = true;

                // Status indicators
                bool reticleControllerExists = BTR.CombatUI.UIReticleController.Instance != null;
                EditorGUILayout.LabelField("Reticle Controller", reticleControllerExists ? "✓ Found" : "✗ Not Found");
                
                if (!reticleControllerExists)
                {
                    EditorGUILayout.HelpBox("UIReticleController.Instance not found. Make sure UIReticleController is active in the scene.", MessageType.Warning);
                }
            }

            serializedObject.ApplyModifiedProperties();

            // Repaint during play mode for debug info
            if (Application.isPlaying && showDebugInfo.boolValue)
            {
                Repaint();
            }
        }
    }
}
#endif
